package com.ruoyi.project.vimCdkSys.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * CDK信息快照DTO
 * 用于存储CDK生成时的用户详细信息快照
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class CdkInfoSnapshotDTO {
    
    /**
     * CDK信息类型（幸运id、全补、半补、转发长智）
     */
    private String cdkInfoType;
    
    /**
     * 快照生成时间
     */
    private String snapshotTime;
    
    /**
     * 用户基本信息
     */
    private UserBasicInfo userBasicInfo;
    
    /**
     * 用户资产信息
     */
    private UserAssetInfo userAssetInfo;
    
    /**
     * 上级用户信息
     */
    private SuperiorUserInfo superiorUserInfo;
    
    /**
     * 用户基本信息内部类
     */
    @Data
    public static class UserBasicInfo {
        private Integer id;
        private String nickname;
        private String username;
        private String phone;
        private String userimage;
        private Integer level;
        private Integer exp;
        private Integer identity;
        private String identityName;
        private Integer state;
        private String stateName;
        private String createTime;
        private String lastLoginTime;
    }
    
    /**
     * 用户资产信息内部类
     */
    @Data
    public static class UserAssetInfo {
        private String balance;
        private String keyAmount;
        private String backpackValue;

        /**
         * 设置余额（保留两位小数）
         */
        public void setBalanceFromBigDecimal(BigDecimal balance) {
            this.balance = formatDecimal(balance);
        }

        /**
         * 设置钥匙数量（保留两位小数）
         */
        public void setKeyAmountFromBigDecimal(BigDecimal keyAmount) {
            this.keyAmount = formatDecimal(keyAmount);
        }

        /**
         * 设置背包价值（保留两位小数）
         */
        public void setBackpackValueFromBigDecimal(BigDecimal backpackValue) {
            this.backpackValue = formatDecimal(backpackValue);
        }

        /**
         * 格式化BigDecimal为两位小数字符串
         */
        private String formatDecimal(BigDecimal value) {
            if (value == null) {
                return "0.00";
            }
            return value.setScale(2, RoundingMode.HALF_UP).toString();
        }
    }
    
    /**
     * 上级用户信息内部类
     */
    @Data
    public static class SuperiorUserInfo {
        private Integer inviteUser;
        private String superiorNickname;
        private String superiorPhone;
        private Integer superiorIdentity;
        private String superiorIdentityName;
        private String superiorDisplayInfo;
    }
    
    /**
     * 从UserDetailInfoVO创建快照
     */
    public static CdkInfoSnapshotDTO fromUserDetailInfo(String cdkInfoType, 
                                                        com.ruoyi.project.vimCdkSys.domain.vo.UserDetailInfoVO userDetailInfo) {
        CdkInfoSnapshotDTO snapshot = new CdkInfoSnapshotDTO();
        snapshot.setCdkInfoType(cdkInfoType);
        snapshot.setSnapshotTime(java.time.LocalDateTime.now().toString());
        
        // 用户基本信息
        UserBasicInfo basicInfo = new UserBasicInfo();
        basicInfo.setId(userDetailInfo.getId());
        basicInfo.setNickname(userDetailInfo.getNickname());
        basicInfo.setUsername(userDetailInfo.getUsername());
        basicInfo.setPhone(userDetailInfo.getPhone());
        basicInfo.setUserimage(userDetailInfo.getUserimage());
        basicInfo.setLevel(userDetailInfo.getLevel());
        basicInfo.setExp(userDetailInfo.getExp());
        basicInfo.setIdentity(userDetailInfo.getIdentity());
        basicInfo.setIdentityName(userDetailInfo.getIdentityName());
        basicInfo.setState(userDetailInfo.getState());
        basicInfo.setStateName(userDetailInfo.getStateName());
        basicInfo.setCreateTime(userDetailInfo.getCreateTime());
        basicInfo.setLastLoginTime(userDetailInfo.getLastLoginTime());
        snapshot.setUserBasicInfo(basicInfo);
        
        // 用户资产信息（保留两位小数精度）
        UserAssetInfo assetInfo = new UserAssetInfo();
        assetInfo.setBalanceFromBigDecimal(userDetailInfo.getBalance());
        assetInfo.setKeyAmountFromBigDecimal(userDetailInfo.getKeyAmount());
        assetInfo.setBackpackValueFromBigDecimal(userDetailInfo.getBackpackValue());
        snapshot.setUserAssetInfo(assetInfo);
        
        // 上级用户信息
        SuperiorUserInfo superiorInfo = new SuperiorUserInfo();
        superiorInfo.setInviteUser(userDetailInfo.getInviteUser());
        superiorInfo.setSuperiorNickname(userDetailInfo.getSuperiorNickname());
        superiorInfo.setSuperiorPhone(userDetailInfo.getSuperiorPhone());
        superiorInfo.setSuperiorIdentity(userDetailInfo.getSuperiorIdentity());
        superiorInfo.setSuperiorIdentityName(userDetailInfo.getSuperiorIdentityName());
        superiorInfo.setSuperiorDisplayInfo(userDetailInfo.getSuperiorDisplayInfo());
        snapshot.setSuperiorUserInfo(superiorInfo);
        
        return snapshot;
    }
}
