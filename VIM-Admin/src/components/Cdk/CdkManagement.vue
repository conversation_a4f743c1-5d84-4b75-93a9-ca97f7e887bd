<template>
  <div class="cdk-management">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="CDK码" prop="cdk">
        <el-input
            v-model="queryParams.cdk"
            placeholder="请输入CDK码"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择CDK类型" clearable>
          <el-option
              v-for="dict in cdk_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select v-model="queryParams.state" placeholder="请选择CDK状态" clearable>
          <el-option
              v-for="dict in cdk_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="CDK信息类型" prop="info">
        <el-select v-model="queryParams.info" placeholder="请选择CDK信息类型" clearable>
          <el-option label="幸运id" value="幸运id" />
          <el-option label="全补" value="全补" />
          <el-option label="半补" value="半补" />
          <el-option label="转发长智" value="转发长智" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:cdk:generate']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Plus"
            @click="handleBatchGenerate"
            v-hasPermi="['system:cdk:batchGenerate']"
        >批量生成
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:cdk:delete']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:cdk:export']"
            :disabled="multiple"
        >导出选中
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="info"
            plain
            icon="Download"
            @click="handleExportAll"
            v-hasPermi="['system:cdk:export']"
        >导出全部
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="cdkList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="id" width="80"/>
      <el-table-column label="CDK码" align="center" prop="cdk" min-width="180">
        <template #default="scope">
          <div class="cdk-code-cell">
            <span class="cdk-code">{{ scope.row.cdk }}</span>
            <CopyButton 
                :copy-text="scope.row.cdk"
                :success-message="`CDK码 ${scope.row.cdk} 已复制到剪贴板`"
                class="copy-btn"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="type" width="100">
        <template #default="scope">
          <dict-tag :options="cdk_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="数量/物品ID" align="center" prop="value" width="120"/>
      <el-table-column label="绑定用户" align="center" width="150">
        <template #default="scope">
          <div v-if="scope.row.foruserId && scope.row.foruserId > 0">
            <el-tag type="info" size="small">{{ scope.row.foruserNickname }}</el-tag>
            <div style="font-size: 12px; color: #909399; margin-top: 2px;">
              {{ scope.row.foruserPhone }}
            </div>
          </div>
          <div v-else-if="scope.row.foruserId === 0">
            <el-tag type="success" size="small">通用CDK</el-tag>
            <div style="font-size: 12px; color: #67c23a; margin-top: 2px;">
              任意用户可兑换
            </div>
          </div>
          <span v-else style="color: #909399;">未绑定</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state" width="100">
        <template #default="scope">
          <dict-tag :options="cdk_state" :value="scope.row.state"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="兑换时间" align="center" prop="useTime" width="160">
        <template #default="scope">
          <span>{{ scope.row.useTime ? parseTime(scope.row.useTime) : '未兑换' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="CDK信息" align="center" width="120">
        <template #default="scope">
          <el-button
              v-if="scope.row.info"
              link
              type="primary"
              icon="InfoFilled"
              @click="handleViewInfo(scope.row)"
              size="small"
          >
            查看快照
          </el-button>
          <span v-else style="color: #909399;">无信息</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:cdk:delete']">
            删除
          </el-button>
          <el-button @click="handleView(scope.row)" v-show="scope.row.state !== 0 " icon="User" link type="danger"
          v-hasPermi="['system:cdk:exchangeDetail']"
          >
            兑换详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import { listCdk, delCdk, batchDelCdk } from '@/api/cdk'
import { parseTime } from '@/utils/ruoyi'
import CopyButton from './CopyButton.vue'

// Props
const props = defineProps({
  showSearch: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['add', 'batch-generate', 'view-exchange-detail', 'view-info'])

const { proxy } = getCurrentInstance()
const { cdk_type, cdk_state } = proxy.useDict("cdk_type", "cdk_state")

// 响应式数据
const loading = ref(false)
const cdkList = ref([])
const total = ref(0)
const ids = ref([])
const multiple = ref(true)
const showSearch = ref(props.showSearch)
const dateRange = ref([])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  cdk: undefined,
  type: undefined,
  state: undefined,
  info: undefined,
  createTime: undefined
})

// 获取CDK列表
const getList = () => {
  loading.value = true
  const params = proxy.addDateRange(queryParams.value, dateRange.value)
  listCdk(params).then(response => {
    cdkList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
  multiple.value = !selection.length
}

// 新增按钮操作
const handleAdd = () => {
  emit('add')
}

// 批量生成按钮操作
const handleBatchGenerate = () => {
  emit('batch-generate')
}

// 删除按钮操作
const handleDelete = (row) => {
  const cdkIds = row.id || ids.value
  proxy.$modal.confirm('是否确认删除CDK编号为"' + cdkIds + '"的数据项？').then(() => {
    return row.id ? delCdk(row.id) : batchDelCdk(cdkIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

// 查看兑换详情
const handleView = (row) => {
  emit('view-exchange-detail', row)
}

// 查看CDK信息快照
const handleViewInfo = (row) => {
  emit('view-info', row)
}

// 导出选中数据
const handleExport = () => {
  // 检查是否有选中的数据
  if (ids.value.length === 0) {
    proxy.$modal.msgError('请选择要导出的CDK数据')
    return
  }

  // 获取选中CDK的CDK码
  const selectedCdks = cdkList.value
    .filter(item => ids.value.includes(item.id))
    .map(item => item.cdk)
    .join(',')

  // 导出选中的CDK数据
  proxy.download('cdk/export', {
    ...queryParams.value,
    cdkCodes: selectedCdks
  }, `cdk_selected_${new Date().getTime()}.xlsx`)
}

// 导出全部数据
const handleExportAll = () => {
  // 导出所有符合筛选条件的CDK数据
  proxy.download('cdk/export', {
    ...queryParams.value
  }, `cdk_all_${new Date().getTime()}.xlsx`)
}

// 暴露方法给父组件
defineExpose({
  getList,
  handleQuery,
  resetQuery
})

// 页面加载时获取列表数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.cdk-management {
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

.cdk-code-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cdk-code {
  flex: 1;
  text-align: left;
}

.copy-btn {
  margin-left: 8px;
}

/* 确保分页组件有足够的空间显示 */
:deep(.pagination-container) {
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 20px 0;
}
</style>
