import request from '@/utils/request'

// 查询CDK列表
export function listCdk(query) {
  return request({
    url: '/cdk/list',
    method: 'get',
    params: query
  })
}

// 查询CDK详细信息
export function getCdk(id) {
  return request({
    url: '/cdk/' + id,
    method: 'get'
  })
}

// 生成单个CDK
export function generateCdk(data) {
  return request({
    url: '/cdk/generate',
    method: 'post',
    data: data
  })
}

// 批量生成CDK
export function batchGenerateCdk(data) {
  return request({
    url: '/cdk/batchGenerate',
    method: 'post',
    data: data
  })
}

// 删除CDK
export function delCdk(id) {
  return request({
    url: '/cdk/' + id,
    method: 'delete'
  })
}

// 批量删除CDK
export function batchDelCdk(ids) {
  return request({
    url: '/cdk/batch/' + ids,
    method: 'delete'
  })
}

// 导出CDK
export function exportCdk(query) {
  return request({
    url: '/cdk/export',
    method: 'post',
    data: query
  })
}

// 查询CDK兑换详情
export function getCdkExchangeDetail(query) {
  return request({
    url: '/cdk/exchangeDetail',
    method: 'get',
    params: query
  })
}

// 搜索用户列表（用于CDK用户选择）
export function searchUsers(query) {
  return request({
    url: '/cdk/searchUsers',
    method: 'get',
    params: query
  })
}

// 查询用户CDK兑换记录
export function getUserCdkExchangeRecords(query) {
  return request({
    url: '/cdk/userExchangeRecords',
    method: 'get',
    params: query
  })
}

// 导出用户CDK兑换记录
export function exportUserCdkExchangeRecords(query) {
  return request({
    url: '/cdk/exportUserExchangeRecords',
    method: 'post',
    data: query
  })
}

// 获取用户详细信息（包含钥匙数量、余额、背包价值）
export function getUserDetailInfo(userId) {
  return request({
    url: '/cdk/getUserDetailInfo/' + userId,
    method: 'get'
  })
}