<template>
  <section class="app-main">
    <div class="main-content">
      <router-view v-slot="{ Component, route }">
        <transition name="fade-transform" mode="out-in">
          <div :key="route.path">
            <keep-alive :include="tagsViewStore.cachedViews">
              <component v-if="!route.meta.link" :is="Component" :key="route.path"/>
            </keep-alive>
          </div>
        </transition>
      </router-view>
      <iframe-toggle />
    </div>
  </section>
</template>

<script setup>
import iframeToggle from "./IframeToggle/index"
import useTagsViewStore from '@/store/modules/tagsView'
import IcpInfo from '@/components/IcpInfo/index.vue'

const tagsViewStore = useTagsViewStore()
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;

  .main-content {
    flex: 1;
    overflow: visible;
    min-height: 0;
  }

  .app-footer {
    padding: 12px 20px;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    text-align: center;
    margin-top: auto;
    font-size: 12px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.3s ease;

    .separator {
      opacity: 0.7;
    }

    :deep(.icp-info.inline) {
      color: #fff;

      .icp-item a {
        color: #fff;

        &:hover {
          color: #ddd;
        }
      }
    }

    // 防止hover时背景变白导致文字不可见
    &:hover {
      background: var(--el-bg-color);

      :deep(.icp-info.inline .icp-item a) {
        color: #ddd;
      }
    }
  }
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>

