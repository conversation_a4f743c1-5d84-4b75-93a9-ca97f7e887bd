<template>
  <div class="app-container">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- CDK管理标签页 -->
      <el-tab-pane label="CDK管理" name="cdkManagement">
        <CdkManagement
            ref="cdkManagementRef"
            @add="handleAdd"
            @batch-generate="handleBatchGenerate"
            @view-exchange-detail="handleViewExchangeDetail"
            @view-info="handleViewInfo"
        />
      </el-tab-pane>

      <!-- 用户兑换记录标签页 -->
      <el-tab-pane label="用户兑换记录" name="userExchangeRecords">
        <UserExchangeRecords ref="userExchangeRecordsRef" />
      </el-tab-pane>
    </el-tabs>

    <!-- CDK新增/编辑对话框 -->
    <CdkFormDialog
        v-model="cdkFormVisible"
        :form-data="cdkForm"
        :title="cdkFormTitle"
        @success="handleCdkFormSuccess"
    />

    <!-- CDK批量生成对话框 -->
    <CdkBatchDialog
        v-model="batchFormVisible"
        @success="handleBatchSuccess"
    />

    <!-- CDK兑换详情对话框 -->
    <CdkExchangeDetailDialog
        v-model="exchangeDetailVisible"
        :cdk-info="currentCdk"
    />

    <!-- CDK生成成功对话框 -->
    <CdkSuccessDialog
        v-model="successDialogVisible"
        :generated-cdks="generatedCdks"
        @continue="handleContinueGenerate"
    />

    <!-- CDK信息快照对话框 -->
    <CdkInfoSnapshotDialog
        v-model="infoSnapshotVisible"
        :cdk-info="currentCdkInfo"
    />
  </div>
</template>

<script setup name="Cdk">
import { ref } from 'vue'
import CdkManagement from '@/components/Cdk/CdkManagement.vue'
import UserExchangeRecords from '@/components/Cdk/UserExchangeRecords.vue'
import CdkFormDialog from '@/components/Cdk/CdkFormDialog.vue'
import CdkBatchDialog from '@/components/Cdk/CdkBatchDialog.vue'
import CdkExchangeDetailDialog from '@/components/Cdk/CdkExchangeDetailDialog.vue'
import CdkSuccessDialog from '@/components/Cdk/CdkSuccessDialog.vue'
import CdkInfoSnapshotDialog from '@/components/Cdk/CdkInfoSnapshotDialog.vue'

// 响应式数据
const activeTab = ref('cdkManagement')
const cdkFormVisible = ref(false)
const batchFormVisible = ref(false)
const exchangeDetailVisible = ref(false)
const successDialogVisible = ref(false)
const infoSnapshotVisible = ref(false)
const cdkFormTitle = ref('')
const currentCdk = ref({})
const currentCdkInfo = ref('')
const generatedCdks = ref([])

// 表单数据
const cdkForm = ref({
  id: undefined,
  cdk: undefined,
  type: '2', // 默认为钥匙类型（字符串）
  value: 1,
  foruser: 0,
  info: '幸运id'
})

// 组件引用
const cdkManagementRef = ref()
const userExchangeRecordsRef = ref()

// 标签页切换处理
const handleTabClick = (tab) => {
  activeTab.value = tab.name
}

// 新增CDK
const handleAdd = () => {
  resetCdkForm()
  cdkFormTitle.value = '新增CDK'
  cdkFormVisible.value = true
}

// 批量生成CDK
const handleBatchGenerate = () => {
  batchFormVisible.value = true
}

// 查看兑换详情
const handleViewExchangeDetail = (row) => {
  currentCdk.value = row
  exchangeDetailVisible.value = true
}

// 查看CDK信息快照
const handleViewInfo = (row) => {
  currentCdkInfo.value = row.info || ''
  infoSnapshotVisible.value = true
}

// CDK表单提交成功
const handleCdkFormSuccess = (result) => {
  cdkFormVisible.value = false
  if (result.type === 'single') {
    // 单个生成成功，显示成功对话框
    generatedCdks.value = [result.data]
    successDialogVisible.value = true
  }
  // 刷新列表
  cdkManagementRef.value?.getList()
}

// 批量生成成功
const handleBatchSuccess = (result) => {
  batchFormVisible.value = false
  // 显示生成成功对话框
  generatedCdks.value = result.data || []
  successDialogVisible.value = true
  // 刷新列表
  cdkManagementRef.value?.getList()
}

// 继续生成
const handleContinueGenerate = () => {
  successDialogVisible.value = false
  batchFormVisible.value = true
}

// 重置CDK表单
const resetCdkForm = () => {
  cdkForm.value = {
    id: undefined,
    cdk: undefined,
    type: '2', // 默认为钥匙类型（字符串）
    value: 1,
    foruser: 0,
    info: '幸运id'
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

/* 确保标签页内容有足够的高度 */
:deep(.el-tabs) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: visible;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow: visible;
}
</style>
