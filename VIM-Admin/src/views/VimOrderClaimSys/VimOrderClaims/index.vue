<template>
  <div class="app-container">
    <!-- 搜索表单区域 -->
    <el-card class="search-card" shadow="hover" v-show="showSearch">
      <el-form :model="queryParams" ref="queryRef" :inline="true" class="search-form">
        <el-row :gutter="20">
          <!-- 按照发货时间倒叙或者默认排序 -->
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item label="排序" prop="orderBy" class="search-item">
              <el-select
                  v-model="queryParams.orderBy"
                  placeholder="请选择排序方式"
                  clearable
                  size="default"
                  style="width: 100%"
                  @change="handleSortChange">
                <el-option label="默认排序" value="default"/>
                <el-option label="发货时间倒序" value="claimTimeDesc"/>
                <el-option label="用户等级升序" value="userLevelAsc"/>
                <el-option label="用户等级降序" value="userLevelDesc"/>
              </el-select>
            </el-form-item>
          </el-col>
        <!-- 发货时间范围选择器 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="8">
          <el-form-item label="发货时间" prop="claimTimeRange" class="search-item">
            <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="提货订单ID" prop="id" class="search-item">
            <el-input
                v-model="queryParams.id"
                placeholder="请输入提货订单ID"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="物品订单ID" prop="oid" class="search-item">
            <el-input
                v-model="queryParams.oid"
                placeholder="请输入物品订单ID"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="用户ID" prop="uid" class="search-item">
            <el-input
                v-model="queryParams.uid"
                placeholder="请输入用户ID"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="状态" prop="state" class="search-item">
            <el-select
                v-model="queryParams.state"
                placeholder="请选择状态"
                clearable
                style="width: 100%"
            >
              <el-option label="发货中" :value="1"/>
              <el-option label="已发货" :value="2"/>
              <el-option label="发货失败" :value="3"/>
              <el-option label="已锁价未发货" :value="4"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="用户手机号" prop="userPhone" class="search-item">
            <el-input
                v-model="queryParams.userPhone"
                placeholder="请输入用户手机号"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item class="search-actions">
            <el-button type="primary" icon="Search" @click="handleQuery" class="search-btn">
              搜索
            </el-button>
            <el-button icon="Refresh" @click="resetQuery" class="reset-btn">
              重置
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <el-row :gutter="10" class="toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="Promotion"
            size="default"
            :disabled="multiple"
            @click="handleBatchAutoShip"
            v-hasPermi="['VimOrderClaimSys:VimOrderClaims:edit']"
            class="batch-btn"
          >
            批量自动发货
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-tag type="info" size="default" class="selection-info" v-if="!multiple">
            已选择 {{ ids.length }} 项
          </el-tag>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="VimOrderClaimsList"
        @selection-change="handleSelectionChange"
        :row-class-name="getRowClassName"
        class="modern-table"
        border
        :header-cell-style="{ background: '#f8f9fa', color: '#495057', fontWeight: '600' }"
      >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="提货订单" width="210" align="center" prop="id"/>
      <el-table-column label="物品订单" width="180" align="center" prop="oid"/>
      <el-table-column label="用户id" align="center" prop="uid" width="70"/>
      <el-table-column label="商品id" align="center" prop="itemid" width="70"/>
      <el-table-column label="商品名" align="center" prop="itemname" width="150" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip effect="dark" :content="scope.row.itemname" placement="top" :disabled="!scope.row.itemname">
            <div class="text-ellipsis">{{ scope.row.itemname }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="商品价格" align="center" prop="itemPrice" width="100">
        <template #default="scope">
          <el-tag type="success" size="small" class="price-tag">
            ¥{{ Number(scope.row.itemPrice || 0).toFixed(2) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="实际发货价格" align="center" prop="cost" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.cost !== null && scope.row.cost !== undefined"
                  type="warning" size="small" class="price-tag">
            ¥{{ Number(scope.row.cost).toFixed(2) }}
          </el-tag>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <el-table-column label="商品英文名" align="center" prop="hashname" width="180" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip effect="dark" :content="scope.row.hashname" placement="top" :disabled="!scope.row.hashname">
            <div class="text-ellipsis">{{ scope.row.hashname }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="userDisplayName" width="100">
        <template #default="scope">
          <el-tooltip effect="dark" :content="scope.row.userDisplayName" placement="top" :disabled="!scope.row.userDisplayName">
            <div class="text-ellipsis user-nickname">{{ scope.row.userDisplayName }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="用户手机号" align="center" prop="userPhoneRaw" width="130">
        <template #default="scope">
          <el-tooltip effect="dark" :content="scope.row.userPhoneRaw" placement="top" :disabled="!scope.row.userPhoneRaw">
            <div class="phone-container">
              <span class="phone-text">{{ scope.row.userPhoneRaw }}</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="用户身份" align="center" prop="userIdentity" width="100">
        <template #default="scope">
          <el-tag
              :type="getIdentityTagType(scope.row.userIdentity)"
              size="small">
            {{ getIdentityDesc(scope.row.userIdentity) }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- 🆕 新增用户等级列 -->
      <el-table-column
          label="用户等级"
          align="center"
          prop="userLevel"
          width="100"
          sortable="custom"
          @sort-change="handleUserLevelSort">
        <template #default="scope">
          <el-tag v-if="scope.row.userLevel !== null && scope.row.userLevel !== undefined"
                  size="small">
            {{ scope.row.userLevel }}级
          </el-tag>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <!-- 🆕 新增用户经验列 -->
      <el-table-column label="用户经验" align="center" prop="userExp" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.userExp !== null && scope.row.userExp !== undefined"
                  type="success"
                  size="small">
            {{ scope.row.userExp }} EXP
          </el-tag>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="creatTime" width="160">
        <template #default="scope">
          <div class="time-container">
            <el-tooltip effect="dark" :content="formatTimestamp(scope.row.creatTime)" placement="top">
              <span class="time-text">{{ formatTimestamp(scope.row.creatTime) }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发货时间" align="center" prop="claimTime" width="160">
        <template #default="scope">
          <div class="time-container">
            <el-tooltip effect="dark" :content="formatTimestamp(scope.row.claimTime)" placement="top">
              <span class="time-text">{{ formatTimestamp(scope.row.claimTime) }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" prop="state" width="90">
        <template #default="scope">
          <el-tag
              :type="getStatusTagType(scope.row.state)"
              size="small">
            {{ getStatusText(scope.row.state) }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- 新增自动发货列 -->
      <el-table-column label="自动发货" align="center" prop="auto" width="90">
        <template #default="scope">
          <!-- 特定Steam链接的行不显示自动发货开关 -->
          <div v-if="isSpecialSteamLink(scope.row)" class="special-link-indicator">
            <el-tag
              :type="getSpecialSteamLinkInfo(scope.row)?.type || 'info'"
              size="small"
              effect="dark"
              class="special-order-tag">
              {{ getSpecialSteamLinkInfo(scope.row)?.text || '特殊订单' }}
            </el-tag>
          </div>
          <!-- 普通订单显示自动发货开关 -->
          <template v-else>
            <!-- 🆕 只有当数据完整且已初始化时才渲染开关 -->
            <el-switch
                v-if="scope.row && scope.row.id && isDataInitialized"
                v-hasPermi="['VimOrderClaimSys:VimOrderClaims:edit']"
                :key="`auto-switch-${scope.row.id}-${isDataInitialized}`"
                v-model="scope.row.auto"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
                inline-prompt
                size="small"
                :disabled="(scope.row.state !== 1 && scope.row.state !== 3)"
                @change="(val) => handleAutoStatusChange(scope.row, val)"
            />
            <!-- 🆕 数据未初始化时显示加载状态 -->
            <div v-else style="font-size: 10px; color: #909399;">
              加载中...
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="action-column" width="180" fixed="right">
        <template #default="scope">
          <div class="action-buttons">
            <!-- 发货中状态的操作按钮 -->
            <template v-if="scope.row.state == 1">
              <el-button
                type="primary"
                size="small"
                icon="Van"
                @click="handleShip(scope.row)"
                class="action-btn primary-btn">
                发货
              </el-button>
              <el-button
                type="warning"
                size="small"
                icon="Lock"
                @click="handleLockPrice(scope.row)"
                class="action-btn warning-btn">
                锁价
              </el-button>
            </template>

            <!-- 已发货状态的操作按钮 -->
            <template v-else-if="scope.row.state == 2">
              <el-button
                type="info"
                size="small"
                icon="View"
                @click="handleDetail(scope.row)"
                class="action-btn info-btn">
                详情
              </el-button>
              <el-button
                type="success"
                size="small"
                icon="Edit"
                @click="handleEditCost(scope.row)"
                v-hasPermi="['VimOrderClaimSys:VimOrderClaims:edit']"
                class="action-btn success-btn">
                改价
              </el-button>
            </template>

            <!-- 发货失败状态的操作按钮 -->
            <template v-else-if="scope.row.state == 3">
              <el-button
                type="danger"
                size="small"
                icon="WarningFilled"
                @click="handleDetail(scope.row)"
                class="action-btn danger-btn">
                失败原因
              </el-button>
            </template>

            <!-- 已锁价未发货状态的操作按钮 -->
            <template v-else-if="scope.row.state == 4">
              <el-button
                type="info"
                size="small"
                icon="View"
                @click="handleDetail(scope.row)"
                class="action-btn info-btn">
                详情
              </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
      </el-table>
    </el-card>
    <!-- 发货弹窗 -->
    <el-dialog title="发货信息" v-model="shipDialogVisible" width="600px">
      <el-form :model="shipForm" label-width="120px">
        <el-form-item label="Steam ID:">
          <el-input v-model="shipForm.steamid" readonly/>
        </el-form-item>
        <el-form-item label="Steam 交易链接:">
          <el-input v-model="shipForm.steamlink" type="textarea" readonly/>
        </el-form-item>
        <el-form-item label="实际发货金额:" prop="cost">
          <el-input-number
              v-model="shipForm.cost"
              :min="0"
              :precision="2"
              :step="0.01"
              placeholder="请输入实际发货金额"
              style="width: 100%"/>
        </el-form-item>
        <el-form-item label="失败原因:" prop="failReason" v-if="showFailReason">
          <el-input
              v-model="shipForm.failReason"
              type="textarea"
              placeholder="请输入发货失败原因"
              :rows="3"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button
              type="danger"
              @click="showFailReason = true"
              v-if="!showFailReason">
            发货失败
          </el-button>
          <el-button
              type="danger"
              @click="handleFailShip"
              :disabled="!shipForm.failReason"
              v-else>
            确认失败
          </el-button>
          <el-button type="primary" @click="confirmShip">确认发货</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情弹窗 -->
    <el-dialog
        :title="detailForm.orderState === 3 ? '发货失败详情' : '订单详情'"
        v-model="detailDialogVisible"
        width="600px"
    >
      <!-- 失败状态显示失败原因 -->
      <div v-if="detailForm.orderState === 3" class="fail-reason-container">
        <el-alert
            title="发货失败"
            type="error"
            :closable="false"
            show-icon
            class="mb-4"
        >
          <template #default>
            <div class="fail-info">
              <p><strong>订单ID：</strong>{{ detailForm.orderId }}</p>
              <p><strong>商品名称：</strong>{{ detailForm.itemname }}</p>
            </div>
          </template>
        </el-alert>

        <el-card class="fail-reason-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon class="warning-icon">
                <WarningFilled/>
              </el-icon>
              <span>失败原因详情</span>
            </div>
          </template>
          <div class="fail-reason-content">
            <el-input
                v-model="detailForm.failReason"
                type="textarea"
                :rows="4"
                readonly
                placeholder="暂无失败原因信息"
                class="fail-reason-textarea"
            />
          </div>
        </el-card>
      </div>

      <!-- 正常状态显示详细信息 -->
      <el-form v-else :model="detailForm" label-width="120px">
        <el-form-item label="订单ID:">
          <el-input v-model="detailForm.orderId" readonly/>
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="detailForm.itemname" readonly/>
        </el-form-item>
        <el-form-item label="Steam ID:">
          <el-input v-model="detailForm.steamid" readonly/>
        </el-form-item>
        <el-form-item label="Steam 交易链接:">
          <el-input v-model="detailForm.steamlink" type="textarea" readonly/>
        </el-form-item>
        <el-form-item label="实际发货价格:">
          <el-input v-model="detailForm.actualCost" readonly>
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="detailForm.orderInfo" label="订单备注:">
          <el-input v-model="detailForm.orderInfo" type="textarea" readonly/>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改发货价格弹窗 -->
    <el-dialog title="修改实际发货价格" v-model="editCostDialogVisible" width="500px">
      <el-form :model="editCostForm" :rules="editCostRules" ref="editCostFormRef" label-width="120px">
        <el-form-item label="订单ID:">
          <el-input v-model="editCostForm.orderId" readonly/>
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="editCostForm.itemname" readonly/>
        </el-form-item>
        <el-form-item label="当前发货价格:">
          <el-input v-model="editCostForm.currentCost" readonly/>
        </el-form-item>
        <el-form-item label="新发货价格:" prop="newCost">
          <el-input-number
              v-model="editCostForm.newCost"
              :min="0"
              :precision="2"
              :step="0.01"
              placeholder="请输入新的实际发货价格"
              style="width: 100%"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editCostDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditCost">确认修改</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 锁价确认弹窗 -->
    <el-dialog title="锁价确认" v-model="lockPriceDialogVisible" width="500px">
      <el-form :model="lockPriceForm" label-width="120px">
        <el-form-item label="订单ID:">
          <el-input v-model="lockPriceForm.orderId" readonly/>
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="lockPriceForm.itemname" readonly/>
        </el-form-item>
        <el-form-item label="Steam 交易链接:">
          <el-input v-model="lockPriceForm.steamlink" type="textarea" readonly/>
        </el-form-item>
        <el-form-item label="锁定价格">
          <el-input-number v-model="lockPriceForm.cost"/>
        </el-form-item>
        <el-alert
            title="确认锁价操作"
            description="锁价后订单状态将变更为'已锁价未发货'，请确认是否继续？"
            type="warning"
            show-icon
            :closable="false"
            style="margin: 20px 0;"/>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="lockPriceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmLockPrice">确认锁价</el-button>
        </div>
      </template>
    </el-dialog>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />


  </div>
</template>

<script setup name="VimOrderClaims">
import {getCurrentInstance, onMounted, onUnmounted, reactive, ref, toRefs} from 'vue';
import {listVimOrderClaims, updateAutoStatus, updateVimOrderClaims, batchAutoShip} from "@/api/VimOrderClaimSys/VimOrderClaims";
import {getUserInfo} from "@/api/VimUserSys/VimUsers";
import {
  WarningFilled,
  Search,
  Van,
  Lock,
  View,
  Edit
} from '@element-plus/icons-vue';

const {proxy} = getCurrentInstance();

// 响应式数据定义
const VimOrderClaimsList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const shipDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const editCostDialogVisible = ref(false);
const lockPriceDialogVisible = ref(false);
const showFailReason = ref(false);
const dateRange = ref([]); // 发货时间日期范围

// 🆕 添加初始化完成标志，防止初始化时触发change事件
const isDataInitialized = ref(false);

// 批量操作相关数据
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);

// 用户身份描述映射
const identityMap = {
  1: '普通用户',
  2: '线上主播',
  3: '线下主播',
  4: '代理'
};

// 获取用户身份描述
const getIdentityDesc = (identity) => {
  return identityMap[identity] || '未知身份';
};

// 获取用户身份标签类型
const getIdentityTagType = (identity) => {
  switch (identity) {
    case 1:
      return 'info';     // 普通用户 - 灰色
    case 2:
      return 'success';  // 线上主播 - 绿色
    case 3:
      return 'warning';  // 线下主播 - 橙色
    default:
      return 'info';    // 默认 - 灰色
  }
};
// 表单数据
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: null,
    oid: null,
    uid: null,
    state: null,
    userPhone: null,
    orderBy: 'default',
    beginClaimTime: null, // 发货时间范围开始
    endClaimTime: null    // 发货时间范围结束
  },
  rules: {},
  shipForm: {
    uid: null,
    steamid: null,
    steamlink: null,
    orderId: null,
    failReason: null,
    cost: null,
  },
  detailForm: {
    orderId: null,
    itemname: null,
    steamid: null,
    steamlink: null,
    actualCost: null,
    orderState: null,
    failReason: null,
    orderInfo: null
  },
  editCostForm: {
    orderId: null,
    itemname: null,
    currentCost: null,
    newCost: null
  },
  lockPriceForm: {
    orderId: null,
    itemname: null,
    steamlink: null,
    cost: 0,
    uid: null,
  },
  editCostRules: {
    newCost: [
      {required: true, message: "新发货价格不能为空", trigger: "blur"},
      {type: 'number', min: 0, message: "发货价格必须大于等于0", trigger: "blur"}
    ]
  }
});

const {queryParams, form, rules, shipForm, detailForm, editCostForm, lockPriceForm, editCostRules} = toRefs(data);


const statusMap = {
  1: '发货中',
  2: '已发货',
  3: '发货失败',
  4: '已锁价未发货'
};

// 注释：初始化数据加载已移至onMounted中，避免重复请求

/** 查询订单发货列表 */
function getList() {
  loading.value = true;
  // 🆕 重置初始化标志，防止数据更新时触发change事件
  isDataInitialized.value = false;

  listVimOrderClaims(queryParams.value)
      .then(response => {
        // 确保state字段为数字类型，解决按钮显示问题
        const rows = (response.rows || []).map(item => ({
          ...item,
          state: Number(item.state), // 强制转换为数字类型
          auto: item.auto !== undefined ? Number(item.auto) : 0 // 🆕 确保auto字段有默认值
        }));
        VimOrderClaimsList.value = rows;
        total.value = response.total || 0;

        // 🆕 使用nextTick确保DOM更新完成后再启用开关事件
        proxy.$nextTick(() => {
          setTimeout(() => {
            isDataInitialized.value = true;
          }, 100); // 延迟100ms确保所有组件都已渲染完成
        });
      })
      .catch(error => {
        console.error("获取订单列表失败:", error);
        proxy.$modal.msgError("获取订单列表失败");
      })
      .finally(() => {
        loading.value = false;
      });
}

/** 获取表格行的类名，用于标记特定Steam链接的行 */
const getRowClassName = ({row}) => {
  if (!row || !row.steamlink) {
    return '';
  }
  if (row.steamlink === 'https://steamcommunity.com/tradeoffer/new/?partner=1513331812&token=1QziIKpO') {
    return 'highlighted-row-primary';
  }
  if (row.steamlink === 'https://steamcommunity.com/tradeoffer/new/?partner=1919657702&token=Sb8MAeP2') {
    return 'highlighted-row-secondary';
  }
  return '';
};

/** 判断是否为特定Steam链接的行 */
const isSpecialSteamLink = (row) => {
  if (!row || !row.steamlink) {
    return false;
  }
  return getRowClassName({row}).length > 0;
};

/** 获取特殊Steam链接的类型和标签信息 */
const getSpecialSteamLinkInfo = (row) => {
  if (!row || !row.steamlink) {
    return null;
  }
  if (row.steamlink === 'https://steamcommunity.com/tradeoffer/new/?partner=1513331812&token=1QziIKpO') {
    return {
      type: 'warning',
      text: '1号商人',
      color: '#e6a23c'
    };
  }
  if (row.steamlink === 'https://steamcommunity.com/tradeoffer/new/?partner=1919657702&token=Sb8MAeP2') {
    return {
      type: 'danger',
      text: '2号商人',
      color: '#f56c6c'
    };
  }
  return null;
};

/** 格式化时间戳 */
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-';
  const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
  const date = new Date(ts);
  const padZero = num => num.toString().padStart(2, '0');

  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
};

/** 获取状态文本 */
const getStatusText = (state) => {
  return statusMap[state] || state;
};

/** 获取状态标签类型 */
const getStatusTagType = (state) => {
  switch (state) {
    case 1:
      return 'warning';  // 发货中 - 橙色
    case 2:
      return 'success';  // 已发货 - 绿色
    case 3:
      return 'danger';   // 发货失败 - 红色
    case 4:
      return 'info';     // 已锁价未发货 - 蓝色
    default:
      return 'info';    // 默认 - 灰色
  }
};

/** 处理发货失败 */
const handleFailShip = async () => {
  try {
    loading.value = true;
    const res = await updateVimOrderClaims({
      id: shipForm.value.orderId,
      state: 3, // 状态设为3表示发货失败
      info: shipForm.value.failReason,
      oid: shipForm.value.oid,
      // claimTime: Math.floor(Date.now() / 1000)
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已标记为发货失败");
      shipDialogVisible.value = false;
      showFailReason.value = false;
      getList(); // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "操作失败");
    }
  } catch (error) {
    console.error("操作失败:", error);
    proxy.$modal.msgError("操作失败");
  } finally {
    loading.value = false;
  }
};

/** 发货操作 */
const handleShip = async (row) => {
  try {
    loading.value = true;
    showFailReason.value = false; // 重置显示状态

    // 检查订单状态，防止重复发货
    if (row.state == 2) {
      proxy.$modal.msgWarning("该订单已发货，无法重复发货");
      return;
    }

    if (row.state == 3) {
      proxy.$modal.msgWarning("该订单发货失败，请先处理失败状态");
      return;
    }

    if (row.state != 1) {
      proxy.$modal.msgWarning("订单状态异常，无法进行发货操作");
      return;
    }

    const res = await getUserInfo(row.uid);
    if (res.code === 200) {
      shipForm.value = {
        uid: row.uid,
        steamid: res.data.steamId,
        steamlink: res.data.steamLink,
        orderId: row.id,
        failReason: '', // 清空失败原因
        oid: row.oid,
        cost: null // 清空实际发货金额
      };
      shipDialogVisible.value = true;
    } else {
      proxy.$modal.msgError(res.msg || "获取用户信息失败");
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    proxy.$modal.msgError("获取用户信息失败");
  } finally {
    loading.value = false;
  }
};

/** 确认发货 */
const confirmShip = async () => {
  try {
    loading.value = true;
    const res = await updateVimOrderClaims({
      id: shipForm.value.orderId,  // 订单ID
      steamid: shipForm.value.steamid,  // SteamID
      steamlink: shipForm.value.steamlink,  // Steam交易链接
      state: 2,
      oid: shipForm.value.oid,
      cost: shipForm.value.cost,  // 实际发货金额
      claimTime: Math.floor(Date.now() / 1000)  // 当前时间戳
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("发货成功");
      shipDialogVisible.value = false;
      getList();  // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "发货失败");
    }
  } catch (error) {
    console.error("发货失败:", error);
    proxy.$modal.msgError("发货失败");
  } finally {
    loading.value = false;
  }
};

/** 查看详情 */
const handleDetail = async (row) => {
  try {
    loading.value = true;

    // 根据订单发货状态选择Steam链接数据源
    let steamId = '未设置';
    let steamLink = '未设置';

    if (row.state == 2) {
      // 已发货状态：使用订单发货记录中的Steam信息
      steamId = row.steamid || '未设置';
      steamLink = row.steamlink || '未设置';

      // 设置订单详情信息
      detailForm.value.orderId = row.id;
      detailForm.value.itemname = row.itemname;
      detailForm.value.steamid = steamId;
      detailForm.value.steamlink = steamLink;
      detailForm.value.actualCost = row.cost ? Number(row.cost).toFixed(2) : '未设置';
      detailForm.value.orderState = Number(row.state);
      detailForm.value.failReason = row.info || '暂无失败原因信息';
      detailForm.value.orderInfo = row.info;
      detailDialogVisible.value = true;
    } else {
      // 未发货状态：获取用户信息以确保Steam信息是最新的
      const res = await getUserInfo(row.uid);
      if (res.code === 200) {
        detailForm.value.orderId = row.id;
        detailForm.value.itemname = row.itemname;
        detailForm.value.steamid = res.data.steamId || '未设置';
        detailForm.value.steamlink = res.data.steamLink || '未设置';
        detailForm.value.actualCost = row.cost ? Number(row.cost).toFixed(2) : '未设置';
        detailForm.value.orderState = Number(row.state);
        detailForm.value.failReason = row.info || '暂无失败原因信息';
        detailForm.value.orderInfo = row.info;
        detailDialogVisible.value = true;
      } else {
        proxy.$modal.msgError(res.msg || "获取用户信息失败");
      }
    }
  } catch (error) {
    console.error("获取订单详情失败:", error);
    proxy.$modal.msgError("获取订单详情失败");
  } finally {
    loading.value = false;
  }
};

/** 修改发货价格操作 */
const handleEditCost = (row) => {
  editCostForm.value = {
    orderId: row.id,
    itemname: row.itemname,
    currentCost: row.cost ? `¥${Number(row.cost).toFixed(2)}` : '未设置',
    newCost: row.cost ? Number(row.cost) : 0
  };
  editCostDialogVisible.value = true;
};

/** 确认修改发货价格 */
const confirmEditCost = async () => {
  try {
    // 表单验证
    await proxy.$refs.editCostFormRef.validate();

    loading.value = true;
    const res = await updateVimOrderClaims({
      id: editCostForm.value.orderId,
      cost: editCostForm.value.newCost
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess("发货价格修改成功");
      editCostDialogVisible.value = false;
      getList(); // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "修改失败");
    }
  } catch (error) {
    if (error !== false) { // 排除表单验证失败的情况
      console.error("修改发货价格失败:", error);
      proxy.$modal.msgError("修改失败");
    }
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []; // 重置日期范围
  proxy.resetForm("queryRef");
  queryParams.value.orderBy = 'default'; // 重置排序为默认值
  queryParams.value.beginClaimTime = null; // 重置开始时间
  queryParams.value.endClaimTime = null; // 重置结束时间
  handleQuery();
}

/** 排序变化处理 */
function handleSortChange() {
  // 保存排序状态到localStorage
  localStorage.setItem('vimOrderClaimSortOrder', queryParams.value.orderBy);
  queryParams.value.pageNum = 1; // 重置到第一页
  getList();
}

/** 用户等级列排序处理 */
function handleUserLevelSort({column, prop, order}) {
  if (order === 'ascending') {
    queryParams.value.orderBy = 'userLevelAsc';
  } else if (order === 'descending') {
    queryParams.value.orderBy = 'userLevelDesc';
  } else {
    queryParams.value.orderBy = 'default';
  }
  // 保存排序状态到localStorage
  localStorage.setItem('vimOrderClaimSortOrder', queryParams.value.orderBy);
  queryParams.value.pageNum = 1; // 重置到第一页
  getList();
}

/** 处理日期范围变化 */
function handleDateRangeChange(val) {
  if (val) {
    // 将日期字符串转换为时间戳（秒）
    const startDate = new Date(val[0]);
    const endDate = new Date(val[1]);
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    queryParams.value.beginClaimTime = Math.floor(startDate.getTime() / 1000);
    queryParams.value.endClaimTime = Math.floor(endDate.getTime() / 1000);
  } else {
    queryParams.value.beginClaimTime = null;
    queryParams.value.endClaimTime = null;
  }
}

/** 复制用户完整手机号 */
function copyUserPhone(phone) {
  if (!phone) {
    proxy.$modal.msgError("手机号为空，无法复制");
    return;
  }

  // 使用Clipboard API复制文本
  navigator.clipboard.writeText(phone)
      .then(() => {
        proxy.$modal.msgSuccess("手机号已复制到剪贴板");
      })
      .catch(err => {
        console.error("复制失败:", err);
        proxy.$modal.msgError("复制失败，请手动复制");
      });
}

/** 处理自动发货状态变更 */
const handleAutoStatusChange = async (row, newValue) => {
  try {
    //  防护检查：确保数据已初始化且row对象完整
    if (!isDataInitialized.value || !row || row.id === undefined) {
      console.warn('数据未初始化或row对象不完整，跳过自动发货状态变更');
      return;
    }

    //  防护检查：确保newValue有效
    if (newValue === undefined || newValue === null) {
      console.warn('新值无效，跳过自动发货状态变更');
      return;
    }

    // 业务逻辑检查：只有发货中(state=1)和发货失败(state=3)的订单可以操作自动发货
    if (row.state !== 1 && row.state !== 3) {
      proxy.$modal.msgWarning("只有发货中或发货失败的订单可以操作自动发货设置");
      // 恢复原状态
      row.auto = row.auto === 1 ? 0 : 1;
      return;
    }

    // 如果是要启用自动发货，显示确认提示
    if (row.auto === 1) {
      const confirmResult = await proxy.$modal.confirm(
          '确定要启用该订单的自动发货功能吗？启用后系统将自动处理发货流程。',
          '启用自动发货确认',
          {
            confirmButtonText: '确定启用',
            cancelButtonText: '取消',
            type: 'warning'
          }
      ).catch(() => {
        // 用户取消，恢复原状态
        row.auto = 0;
        return false;
      });

      if (!confirmResult) {
        return;
      }
    } else {
      // 如果是要禁用自动发货，也显示确认提示
      const confirmResult = await proxy.$modal.confirm(
          '确定要禁用该订单的自动发货功能吗？禁用后需要手动处理发货。',
          '禁用自动发货确认',
          {
            confirmButtonText: '确定禁用',
            cancelButtonText: '取消',
            type: 'warning'
          }
      ).catch(() => {
        // 用户取消，恢复原状态
        row.auto = 1;
        return false;
      });

      if (!confirmResult) {
        return;
      }
    }

    // 调用API更新状态
    loading.value = true;
    const response = await updateAutoStatus({
      id: row.id,
      auto: row.auto
    });

    if (response.code === 200) {
      const statusText = row.auto === 1 ? '启用' : '禁用';
      proxy.$modal.msgSuccess(`自动发货${statusText}成功`);
    } else {
      // API调用失败，恢复原状态
      row.auto = row.auto === 1 ? 0 : 1;
      proxy.$modal.msgError(response.msg || '操作失败');
    }
  } catch (error) {
    console.error('更新自动发货状态失败:', error);
    // 发生异常，恢复原状态
    row.auto = row.auto === 1 ? 0 : 1;
    proxy.$modal.msgError('系统异常，请稍后重试');
  } finally {
    loading.value = false;
  }
};

/** 锁价操作 */
const handleLockPrice = async (row) => {
  try {
    loading.value = true;

    // 检查订单状态
    if (row.state !== 1) {
      proxy.$modal.msgWarning("只有发货中的订单才能进行锁价操作");
      return;
    }

    const res = await getUserInfo(row.uid);
    if (res.code === 200) {
      lockPriceForm.value = {
        orderId: row.id,
        itemname: row.itemname,
        steamlink: res.data.steamLink,
        cost: res.data.cost,
        uid: res.data.uid
      };
      lockPriceDialogVisible.value = true;
    } else {
      proxy.$modal.msgError(res.msg || "获取用户信息失败");
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    proxy.$modal.msgError("获取用户信息失败");
  } finally {
    loading.value = false;
  }
};

/** 确认锁价 */
const confirmLockPrice = async () => {
  try {
    loading.value = true;
    const res = await updateVimOrderClaims({
      id: lockPriceForm.value.orderId,
      steamlink: lockPriceForm.value.steamlink,
      uid: lockPriceForm.value.uid,
      cost: lockPriceForm.value.cost,
      state: 4  // 更新状态为已锁价未发货
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("锁价成功");
      lockPriceDialogVisible.value = false;
      getList();  // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "锁价失败");
    }
  } catch (error) {
    console.error("锁价失败:", error);
    proxy.$modal.msgError("锁价失败");
  } finally {
    loading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  isDataInitialized.value = false;

  // 从localStorage恢复排序状态
  const savedSortOrder = localStorage.getItem('vimOrderClaimSortOrder');
  if (savedSortOrder && savedSortOrder !== 'null') {
    queryParams.value.orderBy = savedSortOrder;
  }

  // 初始化数据
  getList();
});

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

// 批量自动发货
function handleBatchAutoShip() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning('请选择要操作的订单');
    return;
  }

  // 过滤出待发货且未设置自动发货的订单，同时排除特定Steam链接的订单
  const pendingOrders = VimOrderClaimsList.value.filter(item =>
    ids.value.includes(item.id) &&
    item.state === 1 &&
    item.auto !== 1 &&
    !getRowClassName(item)
  );

  if (pendingOrders.length === 0) {
    proxy.$modal.msgWarning('所选订单中没有符合条件的订单（需要是待发货且未设置自动发货的普通订单，特殊Steam链接订单不支持自动发货）');
    return;
  }

  const pendingOrderIds = pendingOrders.map(item => item.id);
  const totalSelected = ids.value.length;
  const validCount = pendingOrderIds.length;
  const invalidCount = totalSelected - validCount;

  // 统计特定Steam链接的订单数量
  const specialLinkCount = VimOrderClaimsList.value.filter(item =>
    ids.value.includes(item.id) && getRowClassName(item)
  ).length;

  let confirmMessage = `确认将 ${validCount} 个订单设置为自动发货状态？`;
  if (invalidCount > 0) {
    confirmMessage += `\n注意：已过滤掉 ${invalidCount} 个不符合条件的订单`;
    if (specialLinkCount > 0) {
      confirmMessage += `（包含 ${specialLinkCount} 个特殊Steam链接订单，${invalidCount - specialLinkCount} 个其他不符合条件的订单）`;
    } else {
      confirmMessage += `（非待发货状态或已设置自动发货）`;
    }
    confirmMessage += `。`;
  }

  proxy.$modal.confirm(confirmMessage).then(() => {
    return batchAutoShip(pendingOrderIds);
  }).then((response) => {
    if (response.code === 200) {
      const result = response.data;
      if (result && typeof result === 'object') {
        const { successCount, failedCount, totalCount } = result;
        if (successCount === totalCount) {
          proxy.$modal.msgSuccess(`批量自动发货设置成功，共处理 ${successCount} 个订单`);
        } else {
          proxy.$modal.msgWarning(`批量操作完成，成功 ${successCount} 个，失败 ${failedCount} 个`);
        }
      } else {
        proxy.$modal.msgSuccess(response.msg || '批量自动发货设置成功');
      }
      getList();
    } else {
      proxy.$modal.msgError(response.msg || '批量自动发货设置失败');
    }
  }).catch(() => {
    proxy.$modal.msgInfo('已取消操作');
  });
}

onUnmounted(() => {
  isDataInitialized.value = false;
});
</script>

<style scoped>
/* ===== 整体布局样式 ===== */
.app-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: calc(100vh - 84px);
}

/* ===== 搜索卡片样式 ===== */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.search-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.header-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.header-title {
  font-size: 16px;
}

.search-form {
  padding: 0;
}

/* ===== 搜索表单项样式 ===== */
.search-item {
  margin-bottom: 20px;
  width: 100%;
}

.search-item :deep(.el-form-item__label) {
  width: 100px !important;
  text-align: right;
  padding-right: 12px;
  font-weight: 500;
  color: #606266;
}

.search-item :deep(.el-input) {
  width: 100%;
}

.search-item :deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-item :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.search-item :deep(.el-select) {
  width: 100%;
}

.search-item :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-item :deep(.el-select .el-input__wrapper:hover) {
  border-color: #409eff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.search-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 0;
  gap: 12px;
}

.search-btn, .reset-btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.reset-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ===== 工具栏样式 ===== */
.toolbar-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
}

.toolbar-row {
  display: flex;
  align-items: center;
}

.batch-btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.batch-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.selection-info {
  border-radius: 6px;
  padding: 8px 12px;
  font-weight: 500;
}

/* ===== 表格卡片样式 ===== */
.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

.modern-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modern-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.modern-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff !important;
  transform: scale(1.001);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-item :deep(.el-form-item__label) {
    width: 80px !important;
  }

  .app-container {
    padding: 10px;
  }
}

/* ===== 文本溢出处理样式 ===== */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  cursor: pointer;
  transition: color 0.3s ease;
}

.text-ellipsis:hover {
  color: #409eff;
}

.user-nickname {
  font-weight: 500;
  color: #606266;
}

.phone-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.phone-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.time-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-text {
  font-size: 12px;
  color: #000;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.price-tag {
  font-weight: 600;
  border-radius: 6px;
}

/* ===== 操作按钮样式 ===== */
.action-column {
  padding: 8px !important;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
  align-items: center;
}

.action-btn {
  border-radius: 6px;
  font-size: 12px;
  padding: 6px 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  min-width: 60px;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.primary-btn {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: white;
}

.primary-btn:hover {
  background: linear-gradient(135deg, #337ecc 0%, #409eff 100%);
  box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
}

.warning-btn {
  background: linear-gradient(135deg, #e6a23c 0%, #f0c674 100%);
  color: white;
}

.warning-btn:hover {
  background: linear-gradient(135deg, #cf9236 0%, #e6a23c 100%);
  box-shadow: 0 3px 12px rgba(230, 162, 60, 0.4);
}

.success-btn {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
}

.success-btn:hover {
  background: linear-gradient(135deg, #5daf34 0%, #67c23a 100%);
  box-shadow: 0 3px 12px rgba(103, 194, 58, 0.4);
}

.info-btn {
  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
  color: white;
}

.info-btn:hover {
  background: linear-gradient(135deg, #82848a 0%, #909399 100%);
  box-shadow: 0 3px 12px rgba(144, 147, 153, 0.4);
}

.danger-btn {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  color: white;
}

.danger-btn:hover {
  background: linear-gradient(135deg, #f25656 0%, #f56c6c 100%);
  box-shadow: 0 3px 12px rgba(245, 108, 108, 0.4);
}

/* ===== 对话框样式 ===== */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}

.dialog-footer .el-button {
  min-width: 100px;
  border-radius: 8px;
  font-weight: 500;
}

.el-form-item__content {
  margin-left: 100px !important;
}

.el-textarea__inner {
  min-height: 80px;
  border-radius: 8px;
}

/* ===== 数据标签样式 ===== */
.no-data {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px dashed #e4e7ed;
}

/* 用户等级标签样式 */
.el-tag.el-tag--primary {
  background: linear-gradient(135deg, #ecf5ff 0%, #d9ecff 100%);
  border-color: #b3d8ff;
  color: #409eff;
  font-weight: 600;
  border-radius: 6px;
}

/* 用户经验标签样式 */
.el-tag.el-tag--success {
  background: linear-gradient(135deg, #f0f9ff 0%, #e1f3d8 100%);
  border-color: #95de64;
  color: #67c23a;
  font-weight: 600;
  border-radius: 6px;
}

/* 用户身份标签样式增强 */
.el-tag.el-tag--info {
  background: linear-gradient(135deg, #f4f4f5 0%, #e9e9eb 100%);
  border-color: #d3d4d6;
  color: #909399;
  font-weight: 500;
  border-radius: 6px;
}

.el-tag.el-tag--warning {
  background: linear-gradient(135deg, #fdf6ec 0%, #faecd8 100%);
  border-color: #f5dab1;
  color: #e6a23c;
  font-weight: 600;
  border-radius: 6px;
}

/* 高亮显示特定Steam链接的行 - 两种不同颜色 */
/* 第一种Steam链接 - 橙色主题 */
:deep(.highlighted-row-primary) {
  background: linear-gradient(135deg, #fdf6ec 0%, #faecd8 100%) !important;
  border-left: 4px solid #e6a23c;
}

:deep(.highlighted-row-primary:hover) {
  background: linear-gradient(135deg, #f9e79f 0%, #f7dc6f 100%) !important;
  transform: scale(1.002);
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.25);
}

/* 第二种Steam链接 - 红色主题 */
:deep(.highlighted-row-secondary) {
  background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%) !important;
  border-left: 4px solid #f56c6c;
}

:deep(.highlighted-row-secondary:hover) {
  background: linear-gradient(135deg, #fadbd8 0%, #f1948a 100%) !important;
  transform: scale(1.002);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.25);
}

/* ===== 失败原因容器样式 ===== */
.fail-reason-container {
  padding: 0;
}

.fail-reason-container .mb-4 {
  margin-bottom: 16px;
}

.fail-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}

.fail-info strong {
  color: #303133;
  font-weight: 600;
}

/* 失败原因卡片样式 */
.fail-reason-card {
  margin-bottom: 16px;
  border: 1px solid #f56c6c;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.1);
}

.fail-reason-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
  border-bottom: 1px solid #f56c6c;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #f56c6c;
}

.warning-icon {
  margin-right: 8px;
  font-size: 16px;
}

.fail-reason-content {
  padding: 8px 0;
}

.fail-reason-textarea :deep(.el-textarea__inner) {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
  font-weight: 500;
  resize: none;
  border-radius: 8px;
}

/* 建议卡片样式 */
.suggestion-card {
  border: 1px solid #e6a23c;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.1);
}

.suggestion-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #fdf6ec 0%, #faecd8 100%);
  border-bottom: 1px solid #e6a23c;
}

.suggestion-card .card-header {
  color: #e6a23c;
}

.info-icon {
  margin-right: 8px;
  font-size: 16px;
}

.suggestion-content {
  padding: 8px 0;
}

.suggestion-list {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.suggestion-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.suggestion-list li:last-child {
  margin-bottom: 0;
}

/* ===== 自动发货开关样式 ===== */
:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
  --el-switch-off-color: #dcdfe6;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #67c23a;
  border-color: #67c23a;
}

/* ===== 特殊订单标签样式 ===== */
.special-link-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.special-link-indicator .el-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  background: linear-gradient(135deg, #f4f4f5 0%, #e9e9eb 100%);
  border-color: #d3d4d6;
  color: #909399;
}

/* ===== 分页组件样式 ===== */
:deep(.el-pagination) {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

:deep(.el-pagination .el-pager li:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}
</style>